2025-07-10 22:34:21,914 - INFO - 成功读取登录配置，用户名: sxjyjrjftjzx1
2025-07-10 22:34:21,914 - INFO - 正在启动Chrome浏览器...
2025-07-10 22:34:21,914 - INFO - ====== WebDriver manager ======
2025-07-10 22:34:23,187 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 22:34:29,396 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 22:34:33,401 - INFO - There is no [win64] chromedriver "140.0.7259.2" for browser google-chrome "140.0.7259" in cache
2025-07-10 22:34:33,401 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 22:39:54,954 - INFO - WebDriver version 140.0.7259.2 selected
2025-07-10 22:39:54,956 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/140.0.7259.2/win32/chromedriver-win32.zip
2025-07-10 22:39:54,956 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/140.0.7259.2/win32/chromedriver-win32.zip
2025-07-10 22:39:55,758 - INFO - Driver downloading response is 200
2025-07-10 22:41:01,947 - INFO - 成功读取登录配置，用户名: sxjyjrjftjzx1
2025-07-10 22:41:01,947 - INFO - 正在启动Chrome浏览器...
2025-07-10 22:41:01,947 - INFO - 正在下载/检查ChromeDriver...
2025-07-10 22:41:01,947 - INFO - ====== WebDriver manager ======
2025-07-10 22:41:03,240 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 22:41:05,074 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 22:41:05,455 - INFO - There is no [win64] chromedriver "140.0.7259.2" for browser google-chrome "140.0.7259" in cache
2025-07-10 22:41:05,455 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 22:42:04,691 - INFO - WebDriver version 140.0.7259.2 selected
2025-07-10 22:42:04,694 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/140.0.7259.2/win32/chromedriver-win32.zip
2025-07-10 22:42:04,694 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/140.0.7259.2/win32/chromedriver-win32.zip
2025-07-10 22:43:09,109 - INFO - Driver downloading response is 200
2025-07-10 22:43:22,970 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 22:43:24,011 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\140.0.7259.2]
2025-07-10 22:43:25,909 - INFO - 自动化特征隐藏完成
2025-07-10 22:43:25,909 - INFO - Chrome浏览器启动成功
2025-07-10 22:43:25,909 - INFO - 正在打开登录页面: https://tiaojie.court.gov.cn/tjzz/#/login
2025-07-10 22:43:29,433 - INFO - 自动化特征隐藏完成
2025-07-10 22:43:29,435 - INFO - 页面标题: 人民法院调解平台 | 登录
2025-07-10 22:43:29,436 - INFO - 当前URL: https://tiaojie.court.gov.cn/tjzz/#/login
2025-07-10 22:43:29,618 - INFO - 登录页面截图已保存
2025-07-10 22:43:29,619 - INFO - 开始执行登录...
2025-07-10 22:43:29,671 - INFO - 用户名输入完成
2025-07-10 22:43:30,715 - INFO - 密码输入完成
2025-07-10 22:43:31,715 - INFO - 查找滑块验证...
2025-07-10 22:43:31,749 - INFO - 找到滑块元素: //div[contains(@class, 'slide')]
2025-07-10 22:43:31,934 - INFO - 尝试策略1: 模拟人工滑动
2025-07-10 22:43:31,974 - INFO - 滑块宽度: 468px
2025-07-10 22:43:31,978 - INFO - 候选轨道宽度: 468px, 潜在距离: 0px
2025-07-10 22:43:31,981 - INFO - 候选轨道宽度: 468px, 潜在距离: 0px
2025-07-10 22:43:32,582 - INFO - 总共滑动距离: 300px
2025-07-10 22:43:35,967 - WARNING - 滑块位置检测失败: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[contains(@class, 'slider-btn')]"}
  (Session info: chrome=140.0.7259.2); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xc24883+65683]
	GetHandleVerifier [0x0xc248c4+65748]
	(No symbol) [0x0xa54a73]
	(No symbol) [0x0xa9e7d7]
	(No symbol) [0x0xa9eb7b]
	(No symbol) [0x0xae7ba2]
	(No symbol) [0x0xac38e4]
	(No symbol) [0x0xae5316]
	(No symbol) [0x0xac3696]
	(No symbol) [0x0xa9247e]
	(No symbol) [0x0xa93344]
	GetHandleVerifier [0x0xea6953+2695523]
	GetHandleVerifier [0x0xea199a+2675114]
	GetHandleVerifier [0x0xc4bf85+227221]
	GetHandleVerifier [0x0xc3bd28+161080]
	GetHandleVerifier [0x0xc424bd+187597]
	GetHandleVerifier [0x0xc2c8b8+98504]
	GetHandleVerifier [0x0xc2ca52+98914]
	GetHandleVerifier [0x0xc1701a+10282]
	BaseThreadInitThunk [0x0x76a77ba9+25]
	RtlInitializeExceptionChain [0x0x7770c36b+107]
	RtlClearBits [0x0x7770c2ef+191]

2025-07-10 22:43:35,970 - INFO - 策略1: 模拟人工滑动成功，滑块已消失
2025-07-10 22:43:35,970 - INFO - 策略1: 模拟人工滑动验证成功
2025-07-10 22:43:36,186 - INFO - 登录按钮点击完成
2025-07-10 22:43:39,186 - INFO - 检查是否有新的验证步骤...
2025-07-10 22:43:39,186 - INFO - 查找滑块验证...
2025-07-10 22:43:39,208 - INFO - 找到滑块元素: //div[contains(@class, 'slide')]
2025-07-10 22:43:39,395 - INFO - 尝试策略1: 模拟人工滑动
2025-07-10 22:43:39,428 - INFO - 滑块宽度: 468px
2025-07-10 22:43:39,432 - INFO - 候选轨道宽度: 468px, 潜在距离: 0px
2025-07-10 22:43:39,435 - INFO - 候选轨道宽度: 468px, 潜在距离: 0px
2025-07-10 22:43:40,035 - INFO - 总共滑动距离: 300px
2025-07-10 22:43:43,326 - INFO - 发现成功提示: 完成
2025-07-10 22:43:43,326 - INFO - 策略1: 模拟人工滑动验证成功
2025-07-10 22:43:48,328 - INFO - 登录后URL: https://tiaojie.court.gov.cn/tjzz/#/login
2025-07-10 22:43:48,513 - ERROR - 登录失败 - 仍在登录页面
2025-07-10 23:20:20,843 - INFO - 成功读取登录配置，用户名: sxjyjrjftjzx1
2025-07-10 23:20:20,843 - INFO - 正在启动Chrome浏览器...
2025-07-10 23:20:20,843 - INFO - 正在下载/检查ChromeDriver...
2025-07-10 23:20:20,843 - INFO - ====== WebDriver manager ======
2025-07-10 23:20:22,129 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 23:20:42,152 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-10 23:20:46,791 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\140.0.7259.2\chromedriver-win32/chromedriver.exe] found in cache
2025-07-10 23:20:47,866 - INFO - 自动化特征隐藏完成
2025-07-10 23:20:47,866 - INFO - Chrome浏览器启动成功
2025-07-10 23:20:47,866 - INFO - 正在打开登录页面: https://tiaojie.court.gov.cn/tjzz/#/login
2025-07-10 23:20:51,216 - INFO - 自动化特征隐藏完成
2025-07-10 23:20:51,218 - INFO - 页面标题: 人民法院调解平台 | 登录
2025-07-10 23:20:51,220 - INFO - 当前URL: https://tiaojie.court.gov.cn/tjzz/#/login
2025-07-10 23:20:51,393 - INFO - 登录页面截图已保存
2025-07-10 23:20:51,393 - INFO - 开始执行登录...
2025-07-10 23:20:51,441 - INFO - 用户名输入完成
2025-07-10 23:20:52,483 - INFO - 密码输入完成
2025-07-10 23:20:53,484 - INFO - 查找滑块验证...
2025-07-10 23:20:53,513 - INFO - 找到滑块元素: //div[contains(@class, 'slide')]
2025-07-10 23:20:53,690 - INFO - 尝试策略1: 模拟人工滑动
2025-07-10 23:20:53,727 - INFO - 滑块宽度: 468px
2025-07-10 23:20:53,730 - INFO - 候选轨道宽度: 468px, 潜在距离: 0px
2025-07-10 23:20:53,733 - INFO - 候选轨道宽度: 468px, 潜在距离: 0px
2025-07-10 23:20:54,334 - INFO - 总共滑动距离: 300px
2025-07-10 23:20:57,632 - WARNING - 滑块位置检测失败: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[contains(@class, 'slider-btn')]"}
  (Session info: chrome=140.0.7259.2); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0xc24883+65683]
	GetHandleVerifier [0x0xc248c4+65748]
	(No symbol) [0x0xa54a73]
	(No symbol) [0x0xa9e7d7]
	(No symbol) [0x0xa9eb7b]
	(No symbol) [0x0xae7ba2]
	(No symbol) [0x0xac38e4]
	(No symbol) [0x0xae5316]
	(No symbol) [0x0xac3696]
	(No symbol) [0x0xa9247e]
	(No symbol) [0x0xa93344]
	GetHandleVerifier [0x0xea6953+2695523]
	GetHandleVerifier [0x0xea199a+2675114]
	GetHandleVerifier [0x0xc4bf85+227221]
	GetHandleVerifier [0x0xc3bd28+161080]
	GetHandleVerifier [0x0xc424bd+187597]
	GetHandleVerifier [0x0xc2c8b8+98504]
	GetHandleVerifier [0x0xc2ca52+98914]
	GetHandleVerifier [0x0xc1701a+10282]
	BaseThreadInitThunk [0x0x76a77ba9+25]
	RtlInitializeExceptionChain [0x0x7770c36b+107]
	RtlClearBits [0x0x7770c2ef+191]

2025-07-10 23:20:57,635 - INFO - 策略1: 模拟人工滑动成功，滑块已消失
2025-07-10 23:20:57,635 - INFO - 策略1: 模拟人工滑动验证成功
2025-07-10 23:20:57,844 - INFO - 登录按钮点击完成
2025-07-10 23:21:00,844 - INFO - 检查是否有新的验证步骤...
2025-07-10 23:21:00,844 - INFO - 查找滑块验证...
2025-07-10 23:21:00,867 - INFO - 找到滑块元素: //div[contains(@class, 'slide')]
2025-07-10 23:21:01,059 - INFO - 尝试策略1: 模拟人工滑动
2025-07-10 23:21:01,093 - INFO - 滑块宽度: 468px
2025-07-10 23:21:01,098 - INFO - 候选轨道宽度: 468px, 潜在距离: 0px
2025-07-10 23:21:01,101 - INFO - 候选轨道宽度: 468px, 潜在距离: 0px
2025-07-10 23:21:01,703 - INFO - 总共滑动距离: 300px
2025-07-10 23:21:04,993 - INFO - 发现成功提示: 完成
2025-07-10 23:21:04,993 - INFO - 策略1: 模拟人工滑动验证成功
2025-07-10 23:21:09,995 - INFO - 登录后URL: https://tiaojie.court.gov.cn/tjzz/#/login
2025-07-10 23:21:10,174 - ERROR - 登录失败 - 仍在登录页面

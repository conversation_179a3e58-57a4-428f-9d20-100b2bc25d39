{"_comment": "未立案数据查询系统 - 数据库配置文件", "_description": "只包含数据库连接配置，其他配置已移至代码中", "database": {"_comment": "MySQL数据库连接配置", "host": "localhost", "port": 3306, "user": "root", "password": "123456", "database": "dl_srjtw_com", "charset": "utf8mb4", "autocommit": true, "connection_timeout": 10, "pool_size": 5, "_notes": ["host: MySQL服务器地址，本地开发使用localhost", "port: MySQL端口，默认3306", "user: 数据库用户名，建议创建专用用户而非使用root", "password: 数据库密码，生产环境建议使用环境变量", "database: 目标数据库名称，包含rk_case表", "charset: 字符集，utf8mb4支持完整UTF-8字符", "autocommit: 自动提交事务", "connection_timeout: 连接超时时间（秒）", "pool_size: 连接池大小"]}, "login": {"_comment": "法院调解系统登录配置", "username": "sxjyjrjftjzx1", "password": "Sxjinyuan@2310"}}
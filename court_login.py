#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
法院调解系统自动登录工具
简化版本，专注于登录功能
"""

import time
import json
import logging
import os
import subprocess
import requests
import zipfile
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains

class CourtLogin:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.url = "https://tiaojie.court.gov.cn/tjzz/#/login"
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/court_login.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """从config.json读取登录配置"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            login_config = config.get('login', {})
            username = login_config.get('username', '')
            password = login_config.get('password', '')
            
            if not username or not password:
                raise ValueError("配置文件中缺少用户名或密码")
            
            self.logger.info(f"成功读取登录配置，用户名: {username}")
            return username, password
            
        except Exception as e:
            self.logger.error(f"读取配置文件失败: {e}")
            raise
    
    def init_browser(self):
        """初始化Chrome浏览器"""
        try:
            self.logger.info("正在启动Chrome浏览器...")
            
            options = Options()

            # 基础选项
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')

            # 反自动化检测选项
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            # 注释掉禁用图片，因为滑块验证需要图片
            # options.add_argument('--disable-images')

            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')

            # 用户代理和指纹伪装
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 实验性选项
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # 禁用自动化标识
            prefs = {
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                # 启用图片显示，滑块验证需要图片
                "profile.managed_default_content_settings.images": 1
            }
            options.add_experimental_option("prefs", prefs)
            
            # 自动下载ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            self.wait = WebDriverWait(self.driver, 15)

            # 执行反检测JavaScript代码
            self.hide_automation_features()

            self.logger.info("Chrome浏览器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"Chrome浏览器启动失败: {e}")
            return False

    def hide_automation_features(self):
        """隐藏自动化特征"""
        try:
            # 隐藏webdriver属性
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)

            # 修改Chrome对象
            self.driver.execute_script("""
                window.chrome = {
                    runtime: {},
                };
            """)

            # 修改权限查询
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'permissions', {
                    get: () => ({
                        query: () => Promise.resolve({ state: 'granted' }),
                    }),
                });
            """)

            # 修改插件数组
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)

            # 修改语言
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
            """)

            self.logger.info("自动化特征隐藏完成")

        except Exception as e:
            self.logger.warning(f"隐藏自动化特征失败: {e}")
    
    def open_login_page(self):
        """打开登录页面"""
        try:
            self.logger.info(f"正在打开登录页面: {self.url}")
            self.driver.get(self.url)
            
            # 等待页面加载
            time.sleep(3)

            # 再次执行反检测代码
            self.hide_automation_features()

            self.logger.info(f"页面标题: {self.driver.title}")
            self.logger.info(f"当前URL: {self.driver.current_url}")

            # 截图保存
            self.driver.save_screenshot("logs/login_page.png")
            self.logger.info("登录页面截图已保存")

            return True
            
        except Exception as e:
            self.logger.error(f"打开登录页面失败: {e}")
            return False
    
    def handle_slider_captcha(self):
        """处理滑块验证 - 多种策略拖到最右边"""
        try:
            self.logger.info("查找滑块验证...")

            # 查找滑块按钮 - 扩展更多选择器
            slider_selectors = [
                "//div[contains(@class, 'slider-btn')]",
                "//span[contains(@class, 'slider-btn')]",
                "//div[contains(@class, 'slide-verify-slider-mask-item')]",
                "//div[contains(@class, 'drag')]",
                "//div[contains(@class, 'slider')]",
                "//div[contains(@class, 'slide')]",
                "//div[contains(@class, 'captcha')]",
                "//div[contains(@class, 'verify')]",
                "//div[contains(@style, 'cursor')]",
                "//div[@role='slider']",
                "//*[contains(text(), '滑动')]",
                "//*[contains(text(), '拖动')]",
                "//*[contains(@title, '滑')]",
                "//canvas",
                "//div[contains(@class, 'geetest')]",
                # 添加更多可能的滑块选择器
                "//div[contains(@class, 'slide-verify')]",
                "//div[contains(@class, 'captcha-slider')]",
                "//div[contains(@class, 'verify-slider')]",
                "//div[contains(@class, 'slider-track')]//div",
                "//div[contains(@class, 'slider-handle')]"
            ]
            
            slider_elem = None
            for selector in slider_selectors:
                try:
                    slider_elem = self.driver.find_element(By.XPATH, selector)
                    if slider_elem.is_displayed():
                        self.logger.info(f"找到滑块元素: {selector}")
                        break
                except:
                    continue
            
            if not slider_elem:
                self.logger.info("未找到滑块验证，检查页面所有可能的验证元素...")

                # 检查页面上所有可能的验证相关元素
                try:
                    all_divs = self.driver.find_elements(By.XPATH, "//div")
                    for div in all_divs[:20]:  # 只检查前20个div
                        if div.is_displayed():
                            class_name = div.get_attribute('class') or ''
                            if any(keyword in class_name.lower() for keyword in ['slider', 'slide', 'captcha', 'verify', 'drag']):
                                self.logger.info(f"发现可能的验证元素: class='{class_name}'")

                    # 检查canvas元素（可能是图形验证码）
                    canvases = self.driver.find_elements(By.TAG_NAME, "canvas")
                    if canvases:
                        self.logger.info(f"发现 {len(canvases)} 个canvas元素，可能包含验证码")

                except Exception as e:
                    self.logger.error(f"检查页面元素失败: {e}")

                return True
            
            # 截图滑块状态
            self.driver.save_screenshot("logs/slider_before.png")

            # 尝试多种滑动策略
            strategies = [
                {"name": "策略1: 模拟人工滑动", "method": "human_like"},
                {"name": "策略2: 直接拖拽到最右边", "method": "direct_drag"},
                {"name": "策略3: JavaScript控制", "method": "javascript"},
                {"name": "策略4: 超大距离拖拽", "method": "large_distance"}
            ]

            for i, strategy in enumerate(strategies):
                self.logger.info(f"尝试{strategy['name']}")

                try:
                    # 重新获取滑块元素
                    current_slider = None
                    for selector in slider_selectors:
                        try:
                            current_slider = self.driver.find_element(By.XPATH, selector)
                            if current_slider.is_displayed():
                                break
                        except:
                            continue

                    if not current_slider:
                        self.logger.info("滑块已消失，验证可能成功")
                        return True

                    if strategy["method"] == "human_like":
                        # 策略1: 模拟人工滑动 - 确保拖到最右边
                        actions = ActionChains(self.driver)
                        actions.click_and_hold(current_slider)

                        # 获取滑块轨道信息 - 改进检测逻辑
                        max_distance = 300  # 默认值
                        try:
                            # 尝试多种方式获取轨道宽度
                            track_candidates = [
                                current_slider.find_element(By.XPATH, "../.."),  # 祖父元素
                                current_slider.find_element(By.XPATH, ".."),     # 父元素
                            ]

                            # 也尝试通过class查找轨道
                            try:
                                track_by_class = self.driver.find_element(By.XPATH, "//div[contains(@class, 'slider-track') or contains(@class, 'slide-verify') or contains(@class, 'captcha-track')]")
                                track_candidates.append(track_by_class)
                            except:
                                pass

                            slider_width = current_slider.size['width']
                            self.logger.info(f"滑块宽度: {slider_width}px")

                            for track_elem in track_candidates:
                                track_width = track_elem.size['width']
                                potential_distance = track_width - slider_width
                                self.logger.info(f"候选轨道宽度: {track_width}px, 潜在距离: {potential_distance}px")

                                # 选择合理的轨道宽度（应该比滑块宽度大很多）
                                if potential_distance > 50 and track_width > slider_width * 1.5:
                                    max_distance = potential_distance
                                    self.logger.info(f"选择轨道宽度: {track_width}px, 最大距离: {max_distance}px")
                                    break

                            # 如果还是没找到合理的轨道，使用固定值
                            if max_distance <= 50:
                                max_distance = 300
                                self.logger.info(f"未找到合理轨道，使用默认最大距离: {max_distance}px")

                        except Exception as e:
                            max_distance = 300
                            self.logger.info(f"轨道检测失败，使用默认最大距离: {max_distance}px, 错误: {e}")

                        # 分段滑动到最右边，确保总距离足够
                        total_moved = 0
                        segments = [60, 80, 70, 90]  # 分段距离

                        for j, dist in enumerate(segments):
                            if total_moved < max_distance:
                                move_dist = min(dist, max_distance - total_moved)
                                actions.move_by_offset(move_dist, 0)
                                total_moved += move_dist
                                time.sleep(0.15)  # 人工延迟

                        # 确保滑到最右边
                        if total_moved < max_distance:
                            remaining = max_distance - total_moved + 20  # 多滑一点
                            actions.move_by_offset(remaining, 0)
                            total_moved += remaining

                        self.logger.info(f"总共滑动距离: {total_moved}px")
                        actions.release()
                        actions.perform()

                    elif strategy["method"] == "direct_drag":
                        # 策略2: 直接拖拽到最右边 - 使用更大的距离
                        actions = ActionChains(self.driver)
                        actions.click_and_hold(current_slider)

                        # 尝试多个大距离
                        distances = [400, 500, 600, 800]
                        for dist in distances:
                            actions.move_by_offset(dist, 0)
                            time.sleep(0.1)

                        actions.release()
                        actions.perform()
                        self.logger.info("直接拖拽完成，总距离: 2300px")

                    elif strategy["method"] == "javascript":
                        # 策略3: 使用JavaScript直接控制
                        try:
                            self.driver.execute_script("""
                                var slider = arguments[0];
                                var track = slider.parentElement;
                                var trackWidth = track.offsetWidth;
                                var sliderWidth = slider.offsetWidth;
                                var maxLeft = trackWidth - sliderWidth;
                                slider.style.left = maxLeft + 'px';

                                // 触发事件
                                var event = new Event('change');
                                slider.dispatchEvent(event);
                            """, current_slider)
                            self.logger.info("JavaScript控制完成")
                        except Exception as e:
                            self.logger.warning(f"JavaScript控制失败: {e}")
                            continue

                    elif strategy["method"] == "large_distance":
                        # 策略4: 超大距离拖拽
                        actions = ActionChains(self.driver)
                        actions.click_and_hold(current_slider)
                        actions.move_by_offset(1000, 0)  # 超大距离
                        actions.release()
                        actions.perform()

                    # 等待验证结果
                    time.sleep(2)

                    # 检查是否成功 - 改进检测逻辑
                    success = False

                    # 方法1: 检查成功提示文字
                    success_texts = ["验证成功", "验证通过", "success", "完成"]
                    for text in success_texts:
                        try:
                            success_elem = self.driver.find_element(By.XPATH, f"//*[contains(text(), '{text}')]")
                            if success_elem.is_displayed():
                                self.logger.info(f"发现成功提示: {text}")
                                success = True
                                break
                        except:
                            continue

                    # 方法2: 检查滑块位置是否在最右边
                    if not success:
                        try:
                            current_slider = self.driver.find_element(By.XPATH, slider_selectors[0])
                            slider_style = current_slider.get_attribute('style')
                            self.logger.info(f"滑块当前样式: {slider_style}")

                            # 检查left值是否接近最大值
                            if 'left:' in slider_style:
                                import re
                                left_match = re.search(r'left:\s*(\d+)', slider_style)
                                if left_match:
                                    left_value = int(left_match.group(1))
                                    self.logger.info(f"滑块当前位置: {left_value}px")
                                    if left_value > 250:  # 假设轨道宽度约300px
                                        success = True
                                        self.logger.info("滑块位置检测成功")
                        except Exception as e:
                            self.logger.warning(f"滑块位置检测失败: {e}")

                    # 方法3: 检查滑块是否还可以拖动
                    if not success:
                        try:
                            current_slider = self.driver.find_element(By.XPATH, slider_selectors[0])
                            if current_slider.is_displayed():
                                self.logger.info(f"{strategy['name']}失败，滑块仍然存在且可见")
                                continue
                        except:
                            self.logger.info(f"{strategy['name']}成功，滑块已消失")
                            success = True

                    if success:
                        self.logger.info(f"{strategy['name']}验证成功")
                        return True

                except Exception as e:
                    self.logger.warning(f"{strategy['name']}执行失败: {e}")
                    continue

            self.logger.error("所有滑动策略都失败了")
            return False
                
        except Exception as e:
            self.logger.error(f"滑块验证处理失败: {e}")
            return False
    
    def login(self, username, password):
        """执行登录"""
        try:
            self.logger.info("开始执行登录...")
            
            # 查找并填写用户名
            username_xpath = "//input[contains(@placeholder, '用户名') or contains(@placeholder, '账号')]"
            username_elem = self.wait.until(EC.presence_of_element_located((By.XPATH, username_xpath)))
            username_elem.clear()
            username_elem.send_keys(username)
            self.logger.info("用户名输入完成")
            time.sleep(1)
            
            # 查找并填写密码
            password_xpath = "//input[@type='password' or contains(@placeholder, '密码')]"
            password_elem = self.wait.until(EC.presence_of_element_located((By.XPATH, password_xpath)))
            password_elem.clear()
            password_elem.send_keys(password)
            self.logger.info("密码输入完成")
            time.sleep(1)
            
            # 处理滑块验证
            if not self.handle_slider_captcha():
                self.logger.warning("滑块验证失败，尝试手动处理")
                input("请手动完成滑块验证后按回车继续...")
            
            # 截图登录前状态
            self.driver.save_screenshot("logs/before_login.png")
            
            # 点击登录按钮 - 使用提供的精确XPath
            login_btn_xpath = "/html/body/div/div/div[2]/div/div/div[2]/form/div[5]/div/button/span"
            login_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, login_btn_xpath)))
            login_btn.click()
            self.logger.info("登录按钮点击完成")

            # 等待登录结果 - 增加等待时间
            time.sleep(3)

            # 检查是否有新的验证码或滑块
            self.logger.info("检查是否有新的验证步骤...")
            if not self.handle_slider_captcha():
                self.logger.warning("可能有新的验证步骤需要处理")

            # 再次等待
            time.sleep(5)
            
            # 检查登录结果
            current_url = self.driver.current_url
            self.logger.info(f"登录后URL: {current_url}")
            
            # 截图登录后状态
            self.driver.save_screenshot("logs/after_login.png")
            
            # 判断登录结果
            if "forgot-password" in current_url or "forget" in current_url:
                self.logger.error("登录失败 - 跳转到忘记密码页面，可能是用户名或密码错误")
                return False
            elif "login" in current_url:
                self.logger.error("登录失败 - 仍在登录页面")
                return False
            else:
                self.logger.info("登录成功！")
                return True
                
        except Exception as e:
            self.logger.error(f"登录过程失败: {e}")
            self.driver.save_screenshot("logs/login_error.png")
            return False
    
    def interactive_mode(self):
        """交互模式"""
        print("\n🤖 进入交互模式")
        print("可用命令:")
        print("  click <xpath>        - 点击元素")
        print("  input <xpath> <text> - 输入文本")
        print("  text <xpath>         - 获取文本")
        print("  screenshot [file]    - 截图")
        print("  url                  - 显示当前URL")
        print("  title                - 显示页面标题")
        print("  quit                 - 退出")
        
        while True:
            try:
                cmd = input("court> ").strip()
                
                if not cmd or cmd == "quit":
                    break
                
                parts = cmd.split(" ", 2)
                action = parts[0]
                
                if action == "click" and len(parts) > 1:
                    try:
                        elem = self.wait.until(EC.element_to_be_clickable((By.XPATH, parts[1])))
                        elem.click()
                        print("✅ 点击成功")
                    except:
                        print("❌ 点击失败")
                
                elif action == "input" and len(parts) > 2:
                    try:
                        elem = self.wait.until(EC.presence_of_element_located((By.XPATH, parts[1])))
                        elem.clear()
                        elem.send_keys(parts[2])
                        print("✅ 输入成功")
                    except:
                        print("❌ 输入失败")
                
                elif action == "text" and len(parts) > 1:
                    try:
                        elem = self.wait.until(EC.presence_of_element_located((By.XPATH, parts[1])))
                        text = elem.text
                        print(f"文本: {text}")
                    except:
                        print("❌ 获取文本失败")
                
                elif action == "screenshot":
                    filename = parts[1] if len(parts) > 1 else f"logs/screenshot_{int(time.time())}.png"
                    self.driver.save_screenshot(filename)
                    print(f"✅ 截图保存: {filename}")
                
                elif action == "url":
                    print(f"当前URL: {self.driver.current_url}")
                
                elif action == "title":
                    print(f"页面标题: {self.driver.title}")
                
                else:
                    print("❌ 未知命令")
            
            except KeyboardInterrupt:
                print("\n用户中断")
                break
            except Exception as e:
                print(f"❌ 命令执行出错: {e}")
    
    def close(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("浏览器已关闭")
        except:
            pass

def main():
    """主函数"""
    court = CourtLogin()
    
    try:
        # 读取配置
        username, password = court.load_config()
        
        # 初始化浏览器
        if not court.init_browser():
            return
        
        # 打开登录页面
        if not court.open_login_page():
            return
        
        # 执行登录
        max_attempts = 3
        for attempt in range(max_attempts):
            print(f"\n🔐 登录尝试 {attempt + 1}/{max_attempts}")
            
            if court.login(username, password):
                print("🎉 登录成功！")
                
                choice = input("\n选择操作: 1=交互模式 2=等待手动操作 [1]: ").strip() or "1"
                
                if choice == "1":
                    court.interactive_mode()
                else:
                    input("按回车键结束...")
                break
            else:
                print(f"❌ 第{attempt + 1}次登录失败")
                
                if attempt < max_attempts - 1:
                    retry = input("是否重新尝试? (y/n) [y]: ").strip().lower()
                    if retry == 'n':
                        break
                    
                    # 重新打开登录页面
                    court.open_login_page()
        else:
            print("❌ 登录失败，已达到最大尝试次数")

            # 提供交互模式选项用于调试
            debug_choice = input("是否进入交互模式进行调试? (y/n) [n]: ").strip().lower()
            if debug_choice == 'y':
                print("进入调试模式，可以手动检查页面状态...")
                court.interactive_mode()
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        court.logger.error(f"程序异常: {e}")
    
    finally:
        court.close()

if __name__ == "__main__":
    main()
